"""
Token manager for automatic ThinkBuddy token refresh
"""
import asyncio
import httpx
import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TokenData:
    """Container for token information"""
    access_token: str
    refresh_token: str
    expires_at: Optional[int] = None
    token_type: str = "bearer"


class TokenManager:
    """Manages automatic token refresh for ThinkBuddy API"""
    
    def __init__(self, api_url: str, initial_access_token: str, initial_refresh_token: str):
        self.api_url = api_url.rstrip('/')
        self.token_data = TokenData(
            access_token=initial_access_token,
            refresh_token=initial_refresh_token
        )
        self.client = httpx.AsyncClient(timeout=30.0)
        self.refresh_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
        # User data from the token request example (keep consistent for all requests)
        self.user_data = {
            "id": "d1c42398-295a-449a-9b69-5d4e0d85ce3a",
            "aud": "authenticated",
            "role": "authenticated",
            "email": "<EMAIL>",
            "email_confirmed_at": "2025-03-18T17:15:11.913088Z",
            "phone": "",
            "confirmed_at": "2025-03-18T17:15:11.913088Z",
            "last_sign_in_at": "2025-06-24T00:32:20.343949Z",
            "app_metadata": {
                "provider": "google",
                "providers": ["google"]
            },
            "user_metadata": {
                "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                "email": "<EMAIL>",
                "email_verified": True,
                "full_name": "Jonathan Noonan",
                "is_onboarded": True,
                "iss": "https://accounts.google.com",
                "name": "Jonathan Noonan",
                "phone_verified": False,
                "picture": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                "provider_id": "108711733011680761102",
                "sub": "108711733011680761102"
            },
            "identities": [
                {
                    "identity_id": "15dd75fe-8303-4e30-82bf-61545ce98779",
                    "id": "108711733011680761102",
                    "user_id": "d1c42398-295a-449a-9b69-5d4e0d85ce3a",
                    "identity_data": {
                        "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                        "email": "<EMAIL>",
                        "email_verified": True,
                        "full_name": "Jonathan Noonan",
                        "iss": "https://accounts.google.com",
                        "name": "Jonathan Noonan",
                        "phone_verified": False,
                        "picture": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                        "provider_id": "108711733011680761102",
                        "sub": "108711733011680761102"
                    },
                    "provider": "google",
                    "last_sign_in_at": "2025-03-18T17:15:11.910128Z",
                    "created_at": "2025-03-18T17:15:11.910181Z",
                    "updated_at": "2025-06-24T00:32:20.339726Z",
                    "email": "<EMAIL>"
                }
            ],
            "created_at": "2025-03-18T17:15:11.907721Z",
            "updated_at": "2025-06-26T02:54:41.377541Z",
            "is_anonymous": False
        }
    
    async def refresh_token(self) -> bool:
        """Refresh the access token using the refresh token"""
        async with self._lock:
            try:
                url = f"{self.api_url}/auth/v1/token?grant_type=refresh_token"
                
                # Create request body with current token data and user info
                request_body = {
                    "access_token": self.token_data.access_token,
                    "token_type": self.token_data.token_type,
                    "expires_in": 3600,
                    "expires_at": int(time.time()) + 3600,
                    "refresh_token": self.token_data.refresh_token,
                    "user": self.user_data
                }
                
                logger.info("Refreshing ThinkBuddy token...")
                logger.debug(f"Token refresh URL: {url}")
                
                response = await self.client.post(
                    url,
                    json=request_body,
                    headers={"Content-Type": "application/json"}
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                # Update token data with new tokens
                self.token_data.access_token = response_data["access_token"]
                self.token_data.refresh_token = response_data["refresh_token"]
                self.token_data.expires_at = response_data.get("expires_at")
                
                logger.info("Successfully refreshed ThinkBuddy token")
                logger.debug(f"New token expires at: {self.token_data.expires_at}")
                
                return True
                
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error refreshing token: {e.response.status_code} - {e.response.text}")
                return False
            except httpx.RequestError as e:
                logger.error(f"Request error refreshing token: {e}")
                return False
            except Exception as e:
                logger.error(f"Unexpected error refreshing token: {e}")
                return False
    
    async def start_auto_refresh(self):
        """Start the automatic token refresh task (every 8 minutes)"""
        if self.refresh_task and not self.refresh_task.done():
            logger.warning("Auto-refresh task already running")
            return
        
        logger.info("Starting automatic token refresh (every 8 minutes)")
        self.refresh_task = asyncio.create_task(self._refresh_loop())
    
    async def stop_auto_refresh(self):
        """Stop the automatic token refresh task"""
        if self.refresh_task and not self.refresh_task.done():
            logger.info("Stopping automatic token refresh")
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass
    
    async def _refresh_loop(self):
        """Background task that refreshes tokens every 8 minutes"""
        while True:
            try:
                # Wait 8 minutes (480 seconds)
                await asyncio.sleep(480)
                
                # Refresh the token
                success = await self.refresh_token()
                if not success:
                    logger.error("Failed to refresh token, will retry in 8 minutes")
                    
            except asyncio.CancelledError:
                logger.info("Token refresh loop cancelled")
                break
            except Exception as e:
                logger.error(f"Unexpected error in token refresh loop: {e}")
                # Continue the loop even if there's an error
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    def get_current_access_token(self) -> str:
        """Get the current access token"""
        return self.token_data.access_token
    
    def get_authorization_header(self) -> str:
        """Get the authorization header value"""
        return f"Bearer {self.token_data.access_token}"
    
    async def close(self):
        """Clean up resources"""
        await self.stop_auto_refresh()
        await self.client.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
