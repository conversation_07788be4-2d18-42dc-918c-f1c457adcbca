#!/usr/bin/env python3
"""
Test script to verify both string and structured content formats work
"""
import asyncio
import httpx


async def test_content_formats():
    async with httpx.AsyncClient() as client:
        base_url = "http://localhost:4635"

        print("Testing both content formats...")

        # Test 1: String content (should work)
        print("\n1. Testing string content format...")
        payload_string = {
            "model": "claude-4-sonnet",
            "messages": [
                {"role": "user", "content": "Say hello in one word"}
            ]
        }

        try:
            resp = await client.post(
                f"{base_url}/v1/chat/completions",
                json=payload_string,
                timeout=30.0
            )
            print(f"String content - Status: {resp.status_code}")
            if resp.status_code == 200:
                data = resp.json()
                print(f"String content - Response: {data['choices'][0]['message']['content']}")
            else:
                print(f"String content - Error: {resp.text}")
        except Exception as e:
            print(f"String content - Request failed: {e}")

        # Test 2: Structured content (was failing with 422)
        print("\n2. Testing structured content format...")
        payload_structured = {
            "model": "claude-4-sonnet",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Say goodbye in one word"}
                    ]
                }
            ]
        }

        try:
            resp = await client.post(
                f"{base_url}/v1/chat/completions",
                json=payload_structured,
                timeout=30.0
            )
            print(f"Structured content - Status: {resp.status_code}")
            if resp.status_code == 200:
                data = resp.json()
                print(f"Structured content - Response: {data['choices'][0]['message']['content']}")
            else:
                print(f"Structured content - Error: {resp.text}")
        except Exception as e:
            print(f"Structured content - Request failed: {e}")

        # Test 3: Mixed structured content (multiple text parts)
        print("\n3. Testing mixed structured content format...")
        payload_mixed = {
            "model": "claude-4-sonnet",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Please respond with "},
                        {"type": "text", "text": "exactly two words"}
                    ]
                }
            ]
        }

        try:
            resp = await client.post(
                f"{base_url}/v1/chat/completions",
                json=payload_mixed,
                timeout=30.0
            )
            print(f"Mixed content - Status: {resp.status_code}")
            if resp.status_code == 200:
                data = resp.json()
                print(f"Mixed content - Response: {data['choices'][0]['message']['content']}")
            else:
                print(f"Mixed content - Error: {resp.text}")
        except Exception as e:
            print(f"Mixed content - Request failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_content_formats())
