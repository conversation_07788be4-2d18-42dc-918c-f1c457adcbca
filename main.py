"""
OpenAI-compatible API server for ThinkBuddy
"""
import os
import logging
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Header, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from models import (
    ChatCompletionRequest, ModelsResponse, ModelInfo,
    ErrorResponse, ErrorDetail
)
from thinkbuddy_client import ThinkBuddyClient
from translator import APITranslator
from token_manager import TokenManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Global variables
thinkbuddy_client: ThinkBuddyClient = None
translator: APITranslator = None
token_manager: TokenManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global thinkbuddy_client, translator, token_manager

    # Startup
    logger.info("Starting OpenAI-compatible API server for ThinkBuddy")

    # Initialize token manager
    api_url = os.getenv("THINKBUDDY_API_URL", "https://api.thinkbuddy.ai")
    access_token = os.getenv("THINKBUDDY_ACCESS_TOKEN")
    refresh_token = os.getenv("THINKBUDDY_REFRESH_TOKEN")

    if not access_token or not refresh_token:
        raise ValueError("THINKBUDDY_ACCESS_TOKEN and THINKBUDDY_REFRESH_TOKEN must be set")

    token_manager = TokenManager(api_url, access_token, refresh_token)

    # Start automatic token refresh
    await token_manager.start_auto_refresh()

    # Initialize ThinkBuddy client with token manager
    thinkbuddy_client = ThinkBuddyClient(api_url, token_manager)

    # Initialize translator
    default_model = os.getenv("DEFAULT_MODEL", "claude-4-sonnet")
    translator = APITranslator(default_model)

    logger.info(f"Initialized with ThinkBuddy API URL: {api_url}")
    logger.info(f"Default model mapping: {default_model}")
    logger.info("Automatic token refresh started (every 8 minutes)")

    yield

    # Shutdown
    logger.info("Shutting down...")
    if token_manager:
        await token_manager.close()
    if thinkbuddy_client:
        await thinkbuddy_client.close()


# Create FastAPI app
app = FastAPI(
    title="OpenAI-compatible API for ThinkBuddy",
    description="A proxy server that translates OpenAI API requests to ThinkBuddy API calls",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log incoming requests for debugging"""
    if request.url.path.startswith("/v1/"):
        logger.info(f"📥 {request.method} {request.url.path}")
        logger.debug(f"Headers: {dict(request.headers)}")

        # Log authorization headers specifically
        auth_header = request.headers.get("authorization")
        api_key_header = request.headers.get("x-api-key")
        if auth_header:
            logger.info(f"🔑 Authorization header present: {auth_header[:20]}...")
        if api_key_header:
            logger.info(f"🔑 X-API-Key header present: {api_key_header[:20]}...")

    response = await call_next(request)

    if request.url.path.startswith("/v1/") and response.status_code >= 400:
        logger.warning(f"❌ {request.method} {request.url.path} -> {response.status_code}")

    return response


def create_error_response(message: str, error_type: str = "invalid_request", status_code: int = 400):
    """Create a standardized error response"""
    error_detail = ErrorDetail(message=message, type=error_type)
    error_response = ErrorResponse(error=error_detail)
    raise HTTPException(status_code=status_code, detail=error_response.model_dump())


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "openai-thinkbuddy-proxy"}


@app.get("/v1/models", response_model=ModelsResponse)
async def list_models():
    """List available models"""
    models = [
        ModelInfo(id="claude-4-sonnet"),
        # Add more models here as they become available:
        # ModelInfo(id="claude-3-opus"),
        # ModelInfo(id="claude-3-sonnet"),
        # ModelInfo(id="claude-3-haiku"),
    ]

    return ModelsResponse(data=models)


async def stream_chat_completion(request: ChatCompletionRequest):
    """Handle streaming chat completion"""
    try:
        # Translate request
        thinkbuddy_request = translator.openai_to_thinkbuddy(request)

        # Stream from ThinkBuddy
        chunk_id = None
        async for chunk_data in thinkbuddy_client.chat_completion_stream(thinkbuddy_request):
            # Parse ThinkBuddy chunk
            chunk = translator.parse_thinkbuddy_stream_chunk(chunk_data)
            if not chunk:
                continue

            # Extract content
            content = translator.extract_content_from_chunk(chunk)
            if content:
                # Create OpenAI-compatible chunk
                if chunk_id is None:
                    chunk_id = f"chatcmpl-{chunk.get('id', 'unknown')}"

                openai_chunk = translator.create_stream_chunk(
                    content=content,
                    original_request=request,
                    chunk_id=chunk_id
                )
                yield openai_chunk

        # Send final [DONE] chunk
        yield translator.create_stream_done_chunk()

    except Exception as e:
        logger.error(f"Error in streaming chat completion: {e}")
        # Send error chunk
        error_chunk = translator.create_stream_chunk(
            content=f"Error: {str(e)}",
            original_request=request,
            finish_reason="error"
        )
        yield error_chunk
        yield translator.create_stream_done_chunk()


@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    authorization: str = Header(None),
    x_api_key: str = Header(None, alias="x-api-key")
):
    """
    Create a chat completion (OpenAI-compatible endpoint)

    This endpoint accepts OpenAI-formatted requests and translates them to ThinkBuddy API calls.
    """
    try:
        logger.info(f"Received chat completion request for model: {request.model}")
        logger.debug(f"Request: {request.model_dump()}")

        # Log authorization info (but don't use it since we use token manager)
        if authorization:
            logger.info(f"🔑 Client provided authorization header")
        if x_api_key:
            logger.info(f"🔑 Client provided x-api-key header")

        # Validate request
        if not request.messages:
            create_error_response("Messages cannot be empty")

        # Handle streaming
        if request.stream:
            return StreamingResponse(
                stream_chat_completion(request),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )

        # Handle non-streaming
        # Translate request
        thinkbuddy_request = translator.openai_to_thinkbuddy(request)

        # Call ThinkBuddy API
        thinkbuddy_response = await thinkbuddy_client.chat_completion(thinkbuddy_request)

        # Translate response
        openai_response = translator.thinkbuddy_to_openai(thinkbuddy_response, request)

        logger.info(f"Successfully processed chat completion request")
        return openai_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat completion: {e}")
        create_error_response(
            f"Internal server error: {str(e)}",
            error_type="internal_error",
            status_code=500
        )


if __name__ == "__main__":
    import uvicorn

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )
