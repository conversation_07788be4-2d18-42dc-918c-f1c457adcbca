# OpenAI-Compatible API for ThinkBuddy

A FastAPI-based proxy server that creates an OpenAI-compatible API layer for ThinkBuddy's backend. This allows AI applications with "OpenAI Compatible API" connection options to work seamlessly with ThinkBuddy's services.

## Features

- **OpenAI API Compatibility**: Implements the `/v1/chat/completions` endpoint with OpenAI-compatible request/response formats
- **Request Translation**: Converts OpenAI-formatted requests to ThinkBuddy's expected format
- **Response Translation**: Converts ThinkBuddy responses back to OpenAI-compatible format
- **Streaming Support**: Supports both streaming and non-streaming chat completions
- **Authentication**: Handles both ApiKey and Authorization headers for ThinkBuddy
- **Model Mapping**: Maps OpenAI model names to ThinkBuddy models
- **Error Handling**: Comprehensive error handling with OpenAI-compatible error responses
- **Health Check**: Basic health monitoring endpoint
- **CORS Support**: Cross-origin resource sharing enabled
- **Automatic Token Refresh**: Automatically refreshes ThinkBuddy tokens every 8 minutes

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd claude-api
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and configure your ThinkBuddy credentials:
   ```env
   THINKBUDDY_API_URL=https://api.thinkbuddy.ai
   THINKBUDDY_ACCESS_TOKEN=your_initial_access_token_here
   THINKBUDDY_REFRESH_TOKEN=your_refresh_token_here
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `THINKBUDDY_API_URL` | ThinkBuddy API base URL | `https://api.thinkbuddy.ai` |
| `THINKBUDDY_ACCESS_TOKEN` | Initial ThinkBuddy access token (required) | - |
| `THINKBUDDY_REFRESH_TOKEN` | ThinkBuddy refresh token (required) | - |
| `HOST` | Server host | `0.0.0.0` |
| `PORT` | Server port | `8000` |
| `LOG_LEVEL` | Logging level | `info` |
| `DEFAULT_MODEL` | Default ThinkBuddy model | `claude-4-sonnet` |

### Available Models

The server currently supports the following models:

| Model ID | Description |
|----------|-------------|
| `claude-4-sonnet` | Claude 4 Sonnet model via ThinkBuddy |

Additional models can be easily added by updating the `MODEL_MAPPING` in `translator.py` and the models list in `main.py`.

### Token Management

The server automatically manages ThinkBuddy authentication tokens:

- **Automatic Refresh**: Tokens are automatically refreshed every 8 minutes (before expiration)
- **Startup Refresh**: The server starts the refresh process immediately on startup
- **Seamless Operation**: API requests continue to work without interruption during token refresh
- **Error Handling**: Failed token refreshes are logged and retried on the next cycle

The server requires both an initial access token and refresh token to start. These tokens are used to maintain continuous authentication with ThinkBuddy's API.

## Usage

### Starting the Server

```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Using with OpenAI Client Libraries

#### Python
```python
from openai import OpenAI

client = OpenAI(
    api_key="any-key",  # Not validated, but required by OpenAI client
    base_url="http://localhost:8000/v1"
)

response = client.chat.completions.create(
    model="claude-4-sonnet",
    messages=[
        {"role": "user", "content": "Hello, how are you?"}
    ]
)

print(response.choices[0].message.content)
```

#### Node.js
```javascript
import OpenAI from 'openai';

const openai = new OpenAI({
    apiKey: 'any-key',
    baseURL: 'http://localhost:8000/v1'
});

const response = await openai.chat.completions.create({
    model: 'claude-4-sonnet',
    messages: [
        { role: 'user', content: 'Hello, how are you?' }
    ]
});

console.log(response.choices[0].message.content);
```

#### Streaming Example
```python
from openai import OpenAI

client = OpenAI(
    api_key="any-key",
    base_url="http://localhost:8000/v1"
)

stream = client.chat.completions.create(
    model="claude-4-sonnet",
    messages=[{"role": "user", "content": "Tell me a story"}],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

## API Endpoints

### Chat Completions
- **POST** `/v1/chat/completions`
- Compatible with OpenAI's chat completions API
- Supports both streaming and non-streaming responses

### Models
- **GET** `/v1/models`
- Returns list of available models in OpenAI format

### Health Check
- **GET** `/health`
- Returns server health status

## Testing

### Basic Test
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

### Streaming Test
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [
      {"role": "user", "content": "Count to 5"}
    ],
    "stream": true
  }'
```

## Project Structure

```
claude-api/
├── main.py                 # FastAPI application
├── models.py              # Pydantic models for API schemas
├── thinkbuddy_client.py   # HTTP client for ThinkBuddy API
├── translator.py          # Translation logic between APIs
├── token_manager.py       # Automatic token refresh management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
└── README.md             # This file
```

## Error Handling

The server provides OpenAI-compatible error responses:

```json
{
  "error": {
    "message": "Error description",
    "type": "invalid_request_error",
    "param": null,
    "code": null
  }
}
```

## Logging

The server uses structured logging with configurable levels:
- `DEBUG`: Detailed request/response information
- `INFO`: General operational information
- `WARNING`: Warning messages
- `ERROR`: Error messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
1. Check the logs for detailed error information
2. Verify your ThinkBuddy credentials are correct
3. Ensure the ThinkBuddy API is accessible
4. Create an issue in the repository
