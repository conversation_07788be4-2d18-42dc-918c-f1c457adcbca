"""
HTTP client for ThinkBuddy API
"""
import httpx
import logging
from typing import Dict, AsyncGenerator
from models import ThinkBuddyRequest, ThinkBuddyResponse
from token_manager import TokenManager

logger = logging.getLogger(__name__)


class ThinkBuddyClient:
    def __init__(self, api_url: str, token_manager: TokenManager):
        self.api_url = api_url.rstrip('/')
        self.token_manager = token_manager
        self.client = httpx.AsyncClient(timeout=60.0)

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for ThinkBuddy API requests"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.token_manager.get_authorization_header()
        }
        return headers

    async def chat_completion(self, request: ThinkBuddyRequest) -> ThinkBuddyResponse:
        """
        Send a non-streaming chat completion request to ThinkBuddy
        """
        try:
            headers = self._get_headers()
            url = f"{self.api_url}/functions/v1/completions"

            # Convert request to dict and ensure stream is False
            request_data = request.model_dump()
            request_data["stream"] = False

            logger.info(f"Sending request to ThinkBuddy: {url}")
            logger.debug(f"Request data: {request_data}")

            response = await self.client.post(
                url,
                headers=headers,
                json=request_data
            )

            response.raise_for_status()
            response_data = response.json()

            logger.debug(f"ThinkBuddy response: {response_data}")

            # Parse response into ThinkBuddyResponse model
            return ThinkBuddyResponse(**response_data)

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error from ThinkBuddy: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error to ThinkBuddy: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error calling ThinkBuddy: {e}")
            raise

    async def chat_completion_stream(self, request: ThinkBuddyRequest) -> AsyncGenerator[str, None]:
        """
        Send a streaming chat completion request to ThinkBuddy
        """
        try:
            headers = self._get_headers()
            url = f"{self.api_url}/functions/v1/completions"

            # Convert request to dict and ensure stream is True
            request_data = request.model_dump()
            request_data["stream"] = True

            logger.info(f"Sending streaming request to ThinkBuddy: {url}")
            logger.debug(f"Request data: {request_data}")

            async with self.client.stream(
                "POST",
                url,
                headers=headers,
                json=request_data
            ) as response:
                response.raise_for_status()

                async for line in response.aiter_lines():
                    if line.strip():
                        # ThinkBuddy should return SSE format: "data: {json}"
                        if line.startswith("data: "):
                            data_part = line[6:]  # Remove "data: " prefix
                            if data_part.strip() == "[DONE]":
                                break
                            yield data_part
                        else:
                            # If not in expected format, yield as-is
                            yield line

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error from ThinkBuddy streaming: {e.response.status_code}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error to ThinkBuddy streaming: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error calling ThinkBuddy streaming: {e}")
            raise

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
